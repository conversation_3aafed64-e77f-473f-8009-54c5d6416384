"""
阶段2: 多尺度深度融合 (Multi-Scale Fusion, MSF)
改进阶段1的简单拼接，实现智能的多尺度深度特征融合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from .conv import Conv


class MultiScaleFusion(nn.Module):
    """多尺度深度融合模块"""
    
    def __init__(self, rgb_channels, depth_channels, fusion_channels=None):
        """
        Args:
            rgb_channels: RGB特征通道数
            depth_channels: 深度特征通道数  
            fusion_channels: 融合后输出通道数，默认等于rgb_channels
        """
        super().__init__()
        
        self.rgb_channels = rgb_channels
        self.depth_channels = depth_channels
        self.fusion_channels = fusion_channels or rgb_channels
        
        # 通道对齐
        self.rgb_align = Conv(rgb_channels, self.fusion_channels, 1) if rgb_channels != self.fusion_channels else nn.Identity()
        self.depth_align = Conv(depth_channels, self.fusion_channels, 1) if depth_channels != self.fusion_channels else nn.Identity()
        
        # 注意力权重生成
        self.attention = ChannelAttention(self.fusion_channels * 2, self.fusion_channels)
        
        # 特征融合
        self.fusion_conv = Conv(self.fusion_channels * 2, self.fusion_channels, 3, 1, 1)
        
        # 残差连接
        self.residual = Conv(rgb_channels, self.fusion_channels, 1) if rgb_channels != self.fusion_channels else nn.Identity()
        
    def forward(self, x):
        """
        Args:
            x: 输入特征，可以是：
               - 列表 [rgb_feat, depth_feat] (来自模型构建框架)
               - 或两个独立参数 rgb_feat, depth_feat (直接调用)
        Returns:
            融合特征 [B, fusion_channels, H, W]
        """
        # 处理输入格式兼容性
        if isinstance(x, (list, tuple)):
            if len(x) != 2:
                raise ValueError(f"MultiScaleFusion expects 2 inputs, got {len(x)}")
            rgb_feat, depth_feat = x[0], x[1]
        else:
            # 如果不是列表，说明是直接调用，需要第二个参数
            # 这种情况下，x 就是 rgb_feat，depth_feat 应该作为第二个参数传入
            # 但由于框架调用方式，这种情况不会发生，所以抛出错误提示
            raise ValueError("MultiScaleFusion expects input as [rgb_feat, depth_feat] list")
        # 通道对齐
        rgb_aligned = self.rgb_align(rgb_feat)
        depth_aligned = self.depth_align(depth_feat)
        
        # 拼接特征
        concat_feat = torch.cat([rgb_aligned, depth_aligned], dim=1)
        
        # 生成注意力权重
        attention_weights = self.attention(concat_feat)
        
        # 加权融合
        weighted_rgb = attention_weights[:, :self.fusion_channels] * rgb_aligned
        weighted_depth = attention_weights[:, self.fusion_channels:] * depth_aligned
        
        # 特征融合
        fused_feat = torch.cat([weighted_rgb, weighted_depth], dim=1)
        fused_feat = self.fusion_conv(fused_feat)
        
        # 残差连接
        residual = self.residual(rgb_feat)
        output = fused_feat + residual
        
        return output


class ChannelAttention(nn.Module):
    """通道注意力模块"""
    
    def __init__(self, in_channels, out_channels, reduction=16):
        super().__init__()
        
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        self.fc = nn.Sequential(
            Conv(in_channels, in_channels // reduction, 1),
            nn.ReLU(inplace=True),
            Conv(in_channels // reduction, out_channels * 2, 1)
        )
        
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        """
        Args:
            x: 输入特征 [B, C, H, W]
        Returns:
            注意力权重 [B, out_channels*2, 1, 1]
        """
        # 全局平均池化和最大池化
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        
        # 融合并生成权重
        attention = self.sigmoid(avg_out + max_out)
        
        return attention


class AdaptiveFusion(nn.Module):
    """自适应融合模块 - 根据特征重要性动态调整融合权重"""
    
    def __init__(self, channels):
        super().__init__()
        
        self.channels = channels
        
        # 特征重要性评估
        self.importance_net = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            Conv(channels, channels // 4, 1),
            nn.ReLU(inplace=True),
            Conv(channels // 4, 2, 1),  # 输出RGB和Depth的重要性权重
            nn.Softmax(dim=1)
        )
        
    def forward(self, x):
        """
        Args:
            x: 输入特征，可以是：
               - 列表 [rgb_feat, depth_feat] (来自模型构建框架)
               - 或两个独立参数 rgb_feat, depth_feat (直接调用)
        Returns:
            自适应融合特征 [B, C, H, W]
        """
        # 处理输入格式兼容性
        if isinstance(x, (list, tuple)):
            if len(x) != 2:
                raise ValueError(f"AdaptiveFusion expects 2 inputs, got {len(x)}")
            rgb_feat, depth_feat = x[0], x[1]
        else:
            raise ValueError("AdaptiveFusion expects input as [rgb_feat, depth_feat] list")
        # 计算特征重要性
        combined = rgb_feat + depth_feat
        importance = self.importance_net(combined)  # [B, 2, 1, 1]
        
        rgb_weight = importance[:, 0:1]  # [B, 1, 1, 1]
        depth_weight = importance[:, 1:2]  # [B, 1, 1, 1]
        
        # 自适应加权融合
        fused = rgb_weight * rgb_feat + depth_weight * depth_feat
        
        return fused


class CrossScaleFusion(nn.Module):
    """跨尺度融合模块 - 融合不同尺度的特征"""

    def __init__(self, channels_list):
        """
        Args:
            channels_list: 不同尺度的通道数列表，如[256, 512, 1024]
        """
        super().__init__()

        self.channels_list = channels_list
        self.num_scales = len(channels_list)

        # 使用统一的中间通道数进行融合
        self.fusion_channels = min(channels_list)  # 使用最小通道数作为融合通道

        # 输入对齐卷积 - 将各尺度对齐到融合通道数
        self.input_convs = nn.ModuleList([
            Conv(channels, self.fusion_channels, 1) for channels in channels_list
        ])

        # 输出恢复卷积 - 将融合结果恢复到原始通道数
        self.output_convs = nn.ModuleList([
            Conv(self.fusion_channels, channels, 1) for channels in channels_list
        ])

        # 跨尺度注意力
        self.cross_attention = nn.ModuleList([
            nn.Sequential(
                Conv(self.fusion_channels, self.fusion_channels // 4, 1),
                nn.ReLU(inplace=True),
                Conv(self.fusion_channels // 4, self.fusion_channels, 1),
                nn.Sigmoid()
            ) for _ in range(self.num_scales)
        ])
        
    def forward(self, features):
        """
        Args:
            features: 不同尺度特征列表 [feat_p3, feat_p4, feat_p5]
        Returns:
            跨尺度融合特征列表
        """
        # 获取目标尺寸（使用最大尺度）
        target_size = features[0].shape[2:]

        # 第一步：通道对齐到融合通道数
        aligned_features = []
        for i, feat in enumerate(features):
            # 通道对齐到融合通道数
            aligned = self.input_convs[i](feat)
            # 空间对齐到最大尺度
            if aligned.shape[2:] != target_size:
                aligned = F.interpolate(aligned, size=target_size, mode='bilinear', align_corners=False)
            aligned_features.append(aligned)

        # 第二步：跨尺度注意力融合
        fused_features = []
        for i in range(self.num_scales):
            # 计算当前尺度与其他尺度的注意力
            current_feat = aligned_features[i]
            attention_sum = 0

            for j, other_feat in enumerate(aligned_features):
                if i != j:
                    attention = self.cross_attention[i](other_feat)
                    attention_sum += attention * other_feat

            # 融合当前尺度和注意力加权的其他尺度
            fused = current_feat + attention_sum / (self.num_scales - 1)

            # 第三步：恢复原始空间尺寸
            if fused.shape[2:] != features[i].shape[2:]:
                fused = F.interpolate(fused, size=features[i].shape[2:], mode='bilinear', align_corners=False)

            # 第四步：恢复原始通道数
            fused = self.output_convs[i](fused)

            fused_features.append(fused)

        return fused_features


class Stage2FusionBlock(nn.Module):
    """阶段2完整融合块 - 整合多种融合策略"""
    
    def __init__(self, rgb_channels, depth_channels, fusion_type='multi_scale'):
        """
        Args:
            rgb_channels: RGB通道数
            depth_channels: 深度通道数
            fusion_type: 融合类型 ('multi_scale', 'adaptive', 'cross_scale')
        """
        super().__init__()
        
        self.fusion_type = fusion_type
        
        if fusion_type == 'multi_scale':
            self.fusion = MultiScaleFusion(rgb_channels, depth_channels)
        elif fusion_type == 'adaptive':
            # 确保通道数一致
            if rgb_channels != depth_channels:
                self.depth_align = Conv(depth_channels, rgb_channels, 1)
            else:
                self.depth_align = nn.Identity()
            self.fusion = AdaptiveFusion(rgb_channels)
        else:
            raise ValueError(f"Unsupported fusion_type: {fusion_type}")
    
    def forward(self, x):
        """
        Args:
            x: 输入特征，可以是：
               - 列表 [rgb_feat, depth_feat] (来自模型构建框架)
               - 或两个独立参数 rgb_feat, depth_feat (直接调用)
        Returns:
            融合后特征
        """
        # 处理输入格式兼容性
        if isinstance(x, (list, tuple)):
            if len(x) != 2:
                raise ValueError(f"Stage2FusionBlock expects 2 inputs, got {len(x)}")
            rgb_feat, depth_feat = x[0], x[1]
        else:
            raise ValueError("Stage2FusionBlock expects input as [rgb_feat, depth_feat] list")

        if self.fusion_type == 'multi_scale':
            return self.fusion([rgb_feat, depth_feat])
        elif self.fusion_type == 'adaptive':
            depth_aligned = self.depth_align(depth_feat)
            return self.fusion([rgb_feat, depth_aligned])


def test_stage2_modules():
    """测试阶段2模块"""
    print("🧪 测试阶段2融合模块...")
    
    # 测试多尺度融合
    rgb_feat = torch.randn(2, 256, 32, 32)
    depth_feat = torch.randn(2, 128, 32, 32)
    
    msf = MultiScaleFusion(256, 128, 256)
    fused = msf([rgb_feat, depth_feat])
    print(f"多尺度融合: {rgb_feat.shape} + {depth_feat.shape} -> {fused.shape}")

    # 测试自适应融合
    depth_aligned = torch.randn(2, 256, 32, 32)
    af = AdaptiveFusion(256)
    adaptive_fused = af([rgb_feat, depth_aligned])
    print(f"自适应融合: {rgb_feat.shape} + {depth_aligned.shape} -> {adaptive_fused.shape}")
    
    # 测试跨尺度融合
    features = [
        torch.randn(2, 256, 64, 64),  # P3
        torch.randn(2, 512, 32, 32),  # P4
        torch.randn(2, 1024, 16, 16)  # P5
    ]
    csf = CrossScaleFusion([256, 512, 1024])
    cross_fused = csf(features)
    print(f"跨尺度融合: {[f.shape for f in features]} -> {[f.shape for f in cross_fused]}")
    
    print("✅ 阶段2模块测试完成!")


if __name__ == '__main__':
    test_stage2_modules()
