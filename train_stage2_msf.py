#!/usr/bin/env python3
"""
阶段2训练脚本: 多尺度深度融合 (Multi-Scale Fusion, MSF)
在阶段1基础上添加智能的多尺度深度特征融合
"""

import sys
from pathlib import Path
import torch
import yaml

# 添加项目根目录到系统路径
ROOT = Path(__file__).resolve().parent
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

from ultralytics.models.yolo.rgbd_model import YOLORGBD
from ultralytics import YOLO


def create_stage2_config():
    """创建阶段2训练配置"""
    config = {
        # 基础配置
        'task': 'detect',
        'mode': 'train',
        'model': 'ultralytics/cfg/models/v12/yolov12-stage2.yaml',
        'data': 'datasets/kitti-stage1-uint8-depth.yaml',
        
        # 训练参数
        'epochs': 300,
        'batch': 16,
        'imgsz': 640,
        'device': '0',
        'workers': 8,
        
        # 优化器设置 - 针对300轮训练优化
        'optimizer': 'AdamW',
        'lr0': 0.001,           # 初始学习率
        'lrf': 0.001,           # 最终学习率 (300轮需要更低的最终lr)
        'momentum': 0.937,
        'weight_decay': 0.0005,
        'warmup_epochs': 5,     # 300轮训练增加预热轮数
        'warmup_momentum': 0.8,
        'warmup_bias_lr': 0.1,
        
        # 数据增强
        'hsv_h': 0.015,
        'hsv_s': 0.7,
        'hsv_v': 0.4,
        'degrees': 0.0,
        'translate': 0.1,
        'scale': 0.5,
        'shear': 0.0,
        'perspective': 0.0,
        'flipud': 0.0,
        'fliplr': 0.5,
        'mosaic': 1.0,
        'mixup': 0.0,
        'copy_paste': 0.0,
        
        # 验证设置 - 针对300轮优化
        'val': True,
        'save': True,
        'save_period': 20,      # 300轮训练，每20轮保存一次
        'cache': False,         # 本地训练建议开启缓存
        'rect': False,
        'cos_lr': True,         # 300轮训练启用余弦学习率
        'close_mosaic': 30,     # 最后30轮关闭mosaic
        'resume': False,
        'amp': True,
        'fraction': 1.0,
        'profile': False,
        'freeze': None,
        'multi_scale': False,
        'overlap_mask': True,
        'mask_ratio': 4,
        'dropout': 0.0,

        # 项目设置
        'project': 'runs/detect',
        'name': 'stage2_msf',
        'exist_ok': False,
        'pretrained': True,
        'verbose': True,
        'seed': 0,
        'deterministic': True,
        'single_cls': False,
        'image_weights': False
    }
    
    return config


def load_stage1_weights(model, stage1_path):
    """从阶段1权重初始化阶段2模型"""
    print(f"📥 从阶段1权重初始化: {stage1_path}")
    
    if not Path(stage1_path).exists():
        print(f"⚠️ 阶段1权重文件不存在: {stage1_path}")
        print("将使用随机初始化")
        return model
    
    try:
        # 加载阶段1权重
        stage1_state = torch.load(stage1_path, map_location='cpu')
        if 'model' in stage1_state:
            stage1_weights = stage1_state['model'].state_dict()
        else:
            stage1_weights = stage1_state
        
        # 获取阶段2模型状态
        stage2_weights = model.model.state_dict()
        
        # 权重迁移
        transferred = 0
        new_modules = 0
        
        for name, param in stage2_weights.items():
            if name in stage1_weights:
                # 检查形状是否匹配
                if param.shape == stage1_weights[name].shape:
                    stage2_weights[name] = stage1_weights[name]
                    transferred += 1
                else:
                    print(f"⚠️ 形状不匹配: {name} {param.shape} vs {stage1_weights[name].shape}")
            else:
                # 新增模块（阶段2特有）
                if 'fusion' in name.lower() or 'attention' in name.lower():
                    new_modules += 1
                    print(f"🆕 新模块: {name}")
        
        # 加载权重
        model.model.load_state_dict(stage2_weights)
        
        print(f"✅ 权重迁移完成:")
        print(f"  - 迁移参数: {transferred}")
        print(f"  - 新增模块: {new_modules}")
        print(f"  - 总参数: {len(stage2_weights)}")
        
    except Exception as e:
        print(f"❌ 权重迁移失败: {e}")
        print("将使用随机初始化")
    
    return model


def train_stage2():
    """训练阶段2模型"""
    print("🚀 开始阶段2训练: 多尺度深度融合 (MSF)")
    print("="*60)
    
    # 创建配置
    config = create_stage2_config()
    
    print("📋 训练配置:")
    print(f"  模型: {config['model']}")
    print(f"  数据: {config['data']}")
    print(f"  轮数: {config['epochs']}")
    print(f"  批大小: {config['batch']}")
    print(f"  学习率: {config['lr0']}")
    print(f"  设备: {config['device']}")
    print(f"  融合类型: {config['stage2_fusion_type']}")
    
    try:
        # 创建模型
        print("\n🏗️ 创建阶段2模型...")
        model = YOLORGBD(config['model'])
        
        # 从阶段1权重初始化
        stage1_path = "best.pt"  # 阶段1训练结果
        model = load_stage1_weights(model, stage1_path)
        
        # 开始训练
        print(f"\n🎯 开始训练...")
        results = model.train(
            data=config['data'],
            epochs=config['epochs'],
            batch=config['batch'],
            imgsz=config['imgsz'],
            device=config['device'],
            workers=config['workers'],
            optimizer=config['optimizer'],
            lr0=config['lr0'],
            lrf=config['lrf'],
            momentum=config['momentum'],
            weight_decay=config['weight_decay'],
            warmup_epochs=config['warmup_epochs'],
            warmup_momentum=config['warmup_momentum'],
            warmup_bias_lr=config['warmup_bias_lr'],
            project=config['project'],
            name=config['name'],
            exist_ok=config['exist_ok'],
            pretrained=False,  # 已经手动加载权重
            verbose=config['verbose'],
            seed=config['seed'],
            deterministic=config['deterministic'],
            val=config['val'],
            save=config['save'],
            save_period=config['save_period'],
            cache=config['cache'],
            amp=config['amp'],
            fraction=config['fraction'],
            profile=config['profile'],
            freeze=config['freeze'],
            multi_scale=config['multi_scale'],
            overlap_mask=config['overlap_mask'],
            mask_ratio=config['mask_ratio'],
            dropout=config['dropout'],
            val_period=config['val_period'],
            # 数据增强参数
            hsv_h=config['hsv_h'],
            hsv_s=config['hsv_s'],
            hsv_v=config['hsv_v'],
            degrees=config['degrees'],
            translate=config['translate'],
            scale=config['scale'],
            shear=config['shear'],
            perspective=config['perspective'],
            flipud=config['flipud'],
            fliplr=config['fliplr'],
            mosaic=config['mosaic'],
            mixup=config['mixup'],
            copy_paste=config['copy_paste'],
            close_mosaic=config['close_mosaic'],
            cos_lr=config['cos_lr'],
        )
        
        print(f"\n🎉 阶段2训练完成!")
        print(f"📊 训练结果:")
        if hasattr(results, 'results_dict'):
            for key, value in results.results_dict.items():
                if isinstance(value, (int, float)):
                    print(f"  {key}: {value:.4f}")
        
        # 保存最终模型
        final_model_path = f"{config['project']}/{config['name']}/weights/best.pt"
        print(f"💾 最佳模型保存至: {final_model_path}")
        
        return results
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def validate_stage2():
    """验证阶段2模型"""
    print("\n🔍 验证阶段2模型...")
    
    try:
        # 加载训练好的模型
        model_path = "runs/detect/stage2_msf/weights/best.pt"
        if not Path(model_path).exists():
            print(f"❌ 模型文件不存在: {model_path}")
            return None
        
        model = YOLORGBD(model_path)
        
        # 验证
        results = model.val(
            data='datasets/kitti-stage1-uint8-depth.yaml',
            device='0',
            batch=16,
            imgsz=640,
            verbose=True
        )
        
        print(f"✅ 验证完成!")
        return results
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return None


def main():
    """主函数"""
    print("🎯 YOLOv12 阶段2: 多尺度深度融合训练")
    print("="*60)
    
    # 检查GPU
    if torch.cuda.is_available():
        print(f"🖥️ GPU: {torch.cuda.get_device_name(0)}")
        print(f"💾 GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB")
    else:
        print("⚠️ 未检测到GPU，将使用CPU训练")
    
    # 训练阶段2
    results = train_stage2()
    
    if results:
        # 验证模型
        validate_stage2()
        
        print(f"\n🎊 阶段2完成!")
        print(f"💡 下一步: 开始阶段3 - 深度引导注意力 (DGA)")
    else:
        print(f"\n❌ 阶段2训练失败")


if __name__ == '__main__':
    main()
