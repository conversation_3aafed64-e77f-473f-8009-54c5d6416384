# YOLOv12 Stage2 - Multi-Scale Fusion (MSF)
# 在Stage1基础上添加多尺度深度融合模块

# Parameters
nc: 3  # number of classes (KITTI: Car, Pedestrian, Cyclist)
scales: # model compound scaling constants, i.e. 'model=yolov12n.yaml' will call yolov12.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]  # YOLOv12n summary: 225 layers,  3157200 parameters,  3157184 gradients,   8.9 GFLOPs
  s: [0.33, 0.50, 1024]  # YOLOv12s summary: 225 layers, 12640656 parameters, 12640640 gradients,  35.5 GFLOPs
  m: [0.67, 0.75, 768]   # YOLOv12m summary: 295 layers, 21816976 parameters, 21816960 gradients,  70.6 GFLOPs
  l: [1.00, 1.00, 512]   # YOLOv12l summary: 365 layers, 43740352 parameters, 43740336 gradients, 196.0 GFLOPs
  x: [1.00, 1.25, 512]   # YOLOv12x summary: 365 layers, 68229648 parameters, 68229632 gradients, 304.4 GFLOPs

# YOLOv12 Stage2 backbone with Multi-Scale Fusion
backbone:
  # [from, repeats, module, args]
  
  # Input processing - 4 channels (RGB + Depth)
  - [-1, 1, InputSeparator, []]  # 0: Split 4-channel input into RGB(3) and Depth(1)
  
  # RGB branch (standard YOLOv12 backbone)
  - [0, 1, Conv, [64, 3, 2]]  # 1-P1/2: RGB Conv 3->64
  - [-1, 1, Conv, [128, 3, 2]]  # 2-P2/4: RGB Conv 64->128
  - [-1, 3, C3k2, [256, False, 0.25]]  # 3: RGB C3k2
  - [-1, 1, Conv, [256, 3, 2]]  # 4-P3/8: RGB Conv 128->256
  - [-1, 6, C3k2, [512, False, 0.25]]  # 5: RGB C3k2
  - [-1, 1, Conv, [512, 3, 2]]  # 6-P4/16: RGB Conv 256->512
  - [-1, 6, C3k2, [1024, True]]  # 7: RGB C3k2
  - [-1, 1, Conv, [1024, 3, 2]]  # 8-P5/32: RGB Conv 512->1024
  - [-1, 3, C3k2, [1024, True]]  # 9: RGB C3k2
  
  # Depth branch (lightweight)
  - [0, 1, DepthGetter, []]  # 10: Extract depth channel
  - [-1, 1, DepthConv, [32, 3, 2]]  # 11-P1/2: Depth Conv 1->32
  - [-1, 1, DepthConv, [64, 3, 2]]  # 12-P2/4: Depth Conv 32->64
  - [-1, 1, DepthConv, [128, 3, 2]]  # 13-P3/8: Depth Conv 64->128
  - [-1, 1, DepthConv, [256, 3, 2]]  # 14-P4/16: Depth Conv 128->256
  - [-1, 1, DepthConv, [512, 3, 2]]  # 15-P5/32: Depth Conv 256->512
  
  # Stage2: Multi-Scale Fusion at P3, P4, P5 levels
  # 修正融合通道数，避免参数爆炸：融合通道数应与RGB通道数匹配
  - [[5, 13], 1, MultiScaleFusion, [384, 96, 384]]   # 16: P3 fusion (RGB 384 + Depth 96 -> 384)
  - [[7, 14], 1, MultiScaleFusion, [576, 192, 576]]  # 17: P4 fusion (RGB 576 + Depth 192 -> 576)
  - [[9, 15], 1, MultiScaleFusion, [576, 384, 576]]  # 18: P5 fusion (RGB 576 + Depth 384 -> 576)

# YOLOv12 head
head:
  # [from, repeats, module, args]
  - [-1, 1, nn.Upsample, [None, 2, 'nearest']]  # 19: P5 upsample
  - [[-1, 17], 1, Concat, [1]]  # 20: Concat P5_up + P4_fused
  - [-1, 3, C3k2, [512, False]]  # 21: C3k2

  - [-1, 1, nn.Upsample, [None, 2, 'nearest']]  # 22: P4 upsample  
  - [[-1, 16], 1, Concat, [1]]  # 23: Concat P4_up + P3_fused
  - [-1, 3, C3k2, [256, False]]  # 24: C3k2

  - [-1, 1, Conv, [256, 3, 2]]  # 25: P3 downsample
  - [[-1, 21], 1, Concat, [1]]  # 26: Concat P3_down + P4
  - [-1, 3, C3k2, [512, False]]  # 27: C3k2

  - [-1, 1, Conv, [512, 3, 2]]  # 28: P4 downsample
  - [[-1, 18], 1, Concat, [1]]  # 29: Concat P4_down + P5_fused
  - [-1, 3, C3k2, [1024, True]]  # 30: C3k2

  - [[24, 27, 30], 1, Detect, [nc]]  # 31: Detect(P3, P4, P5)
