# KITTI RGB+Depth Dataset Configuration for YOLOv12 Stage1
# 专门针对uint8预处理深度图的配置

path: /home/<USER>/kitti  # 服务器数据集根目录
train: images/train_set  # 训练集图像目录
val: images/val_set      # 验证集图像目录

# 深度图路径配置
depth_path: /home/<USER>/kitti/depths  # 深度图根目录

# uint8深度图专用配置
# 根据您的数据分析：
# - 数据类型：uint8
# - 数值范围：[0, 255]
# - 非零像素比例：99.6%-100%
# - 平均值范围：[53.26, 104.31]
# - 0值表示无效深度
depth_format: "uint8_normalized"  # uint8归一化深度图
depth_scale: 1.0                  # 不需要缩放
max_depth: 80.0                   # 假设255对应最大深度80米

# 可选的深度图处理策略：
# "uint8_direct" - 直接使用原始值
# "uint8_normalized" - 假设已归一化，重新映射到物理深度
# "uint8_inverse" - 反向深度编码（255=近，1=远）

# 数据增强配置（针对RGB+D优化）
hsv_h: 0.015            # 色调增强范围
hsv_s: 0.7              # 饱和度增强范围  
hsv_v: 0.4              # 明度增强范围
degrees: 0.0            # 旋转角度（深度图不适合大角度旋转）
translate: 0.1          # 平移范围
scale: 0.5              # 缩放范围
shear: 0.0              # 剪切（深度图不适合剪切）
perspective: 0.0        # 透视变换（深度图不适合）
flipud: 0.0             # 垂直翻转概率
fliplr: 0.5             # 水平翻转概率
mosaic: 1.0             # Mosaic增强概率
mixup: 0.0              # Mixup增强概率（RGB+D不适合）
copy_paste: 0.0         # Copy-paste增强概率

# KITTI数据集类别
nc: 3  # number of classes
names: ['Car', 'Pedestrian', 'Cyclist'] # class names

# 类别权重（可选，用于处理类别不平衡）
class_weights: [1.0, 2.0, 2.0]  # 对应 Car, Pedestrian, Cyclist
