# 阶段1：SimplifiedDepthExtractor 架构设计

## 总体架构
```
输入: RGB (3通道) + Depth (1通道) = 4通道总输入
│
├── RGB Branch (标准YOLOv12 backbone)
│   ├── Conv 64, 3x3, s=2
│   ├── Conv 128, 3x3, s=2
│   └── ... (标准backbone)
│
└── Depth Branch (SimplifiedDepthExtractor)
    ├── DepthConv 32, 3x3, s=2      # P1/2 - 深度初始特征
    ├── DepthConv 64, 3x3, s=2      # P2/4 - 浅层深度特征  
    ├── DepthConv 128, 3x3, s=2     # P3/8 - 中层深度特征
    ├── DepthConv 256, 3x3, s=2     # P4/16 - 深层深度特征
    └── DepthConv 512, 3x3, s=2     # P5/32 - 最深深度特征
│
融合策略 (简单add/concat)
├── P3: RGB_256 + Depth_128 → 384通道
├── P4: RGB_512 + Depth_256 → 768通道  
└── P5: RGB_1024 + Depth_512 → 1536通道
```

## SimplifiedDepthExtractor 设计原则
1. **轻量级**: 比RGB分支轻2-4倍的参数量
2. **多尺度**: 提取P2到P5的深度特征
3. **兼容性**: 输出特征与RGB特征尺寸匹配
4. **简单融合**: 使用element-wise addition或concatenation

## 关键模块设计

### DepthConv Block
```python
class DepthConv(nn.Module):
    def __init__(self, c1, c2, k=3, s=1, p=None, g=1):
        super().__init__()
        self.conv = Conv(c1, c2, k, s, autopad(k, p), g)
        self.bn = nn.BatchNorm2d(c2)
        self.act = nn.SiLU()
    
    def forward(self, x):
        return self.act(self.bn(self.conv(x)))
```

### SimplifiedDepthExtractor
```python
class SimplifiedDepthExtractor(nn.Module):
    def __init__(self):
        super().__init__()
        # 深度特征提取层
        self.depth_conv1 = DepthConv(1, 32, 3, 2)    # 1->32, /2
        self.depth_conv2 = DepthConv(32, 64, 3, 2)   # 32->64, /4  
        self.depth_conv3 = DepthConv(64, 128, 3, 2)  # 64->128, /8
        self.depth_conv4 = DepthConv(128, 256, 3, 2) # 128->256, /16
        self.depth_conv5 = DepthConv(256, 512, 3, 2) # 256->512, /32
        
    def forward(self, depth):
        # depth: [B, 1, H, W]
        d1 = self.depth_conv1(depth)  # [B, 32, H/2, W/2]
        d2 = self.depth_conv2(d1)     # [B, 64, H/4, W/4] 
        d3 = self.depth_conv3(d2)     # [B, 128, H/8, W/8]
        d4 = self.depth_conv4(d3)     # [B, 256, H/16, W/16]
        d5 = self.depth_conv5(d4)     # [B, 512, H/32, W/32]
        
        return {
            'P2': d2,   # 64 channels
            'P3': d3,   # 128 channels  
            'P4': d4,   # 256 channels
            'P5': d5    # 512 channels
        }
```

## 融合策略
- **P3层**: RGB(256) + Depth(128) → Concat → 384通道
- **P4层**: RGB(512) + Depth(256) → Concat → 768通道  
- **P5层**: RGB(1024) + Depth(512) → Concat → 1536通道

## 预期效果
1. 增加约20-30%的参数量（相比纯RGB）
2. 提供基础的深度信息
3. 为后续阶段的复杂融合打基础
4. 验证双模态输入的可行性