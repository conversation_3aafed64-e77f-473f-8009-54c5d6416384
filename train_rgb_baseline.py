#!/usr/bin/env python3
"""
YOLOv12 RGB Baseline Training Script
纯RGB基线模型训练脚本，用于与RGB+Depth模型对比性能

使用标准的YOLOv12架构，只处理RGB输入，不包含深度信息。
"""

import argparse
import os
import sys
from pathlib import Path

# 添加项目根目录到系统路径
ROOT = Path(__file__).resolve().parent
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

from ultralytics import Y<PERSON><PERSON>


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='YOLOv12 RGB Baseline Training')
    
    # 基础参数
    parser.add_argument('--cfg', type=str, default='ultralytics/cfg/models/v12/yolov12-rgb-baseline.yaml',
                       help='model configuration file')
    parser.add_argument('--data', type=str, default='datasets/kitti-rgb-baseline.yaml', 
                       help='dataset YAML file')
    parser.add_argument('--weights', type=str, default='yolov12m.pt',
                       help='initial weights path (optional)')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=300, help='number of epochs')
    parser.add_argument('--batch-size', type=int, default=16, help='batch size')
    parser.add_argument('--imgsz', type=int, default=640, help='image size')
    parser.add_argument('--device', type=str, default='0', help='device to use')
    parser.add_argument('--workers', type=int, default=8, help='number of workers')
    
    # 输出参数
    parser.add_argument('--project', type=str, default='runs/detect', help='project directory')
    parser.add_argument('--name', type=str, default='rgb_baseline', help='experiment name')
    parser.add_argument('--save-period', type=int, default=10, help='save checkpoint every x epochs')
    
    # 其他参数
    parser.add_argument('--cache', action='store_true', help='cache images for faster training')
    parser.add_argument('--resume', type=str, default='', help='resume training from checkpoint')
    
    return parser.parse_args()


def check_environment():
    """检查训练环境"""
    print("🔍 检查训练环境...")
    
    # 检查CUDA
    import torch
    if torch.cuda.is_available():
        print(f"✅ CUDA可用: {torch.cuda.get_device_name()}")
        print(f"📊 GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB")
    else:
        print("⚠️ CUDA不可用，将使用CPU训练")
    
    # 检查内存
    import psutil
    memory = psutil.virtual_memory()
    print(f"💾 系统内存: {memory.total / 1e9:.1f}GB (可用: {memory.available / 1e9:.1f}GB)")


def check_dataset_paths(data_config):
    """检查数据集路径"""
    print("🔍 检查数据集路径...")
    
    # 读取数据集配置
    import yaml
    with open(data_config, 'r', encoding='utf-8') as f:
        data = yaml.safe_load(f)
    
    dataset_root = data['path']
    train_path = os.path.join(dataset_root, data['train'])
    val_path = os.path.join(dataset_root, data['val'])
    
    print(f"📁 数据集路径: {dataset_root}")
    print(f"📁 训练集路径: {train_path}")
    print(f"📁 验证集路径: {val_path}")
    
    # 检查路径是否存在
    if not os.path.exists(dataset_root):
        raise FileNotFoundError(f"数据集根目录不存在: {dataset_root}")
    
    if not os.path.exists(train_path):
        raise FileNotFoundError(f"训练集目录不存在: {train_path}")
    
    if not os.path.exists(val_path):
        raise FileNotFoundError(f"验证集目录不存在: {val_path}")
    
    # 统计图像数量
    train_images = len([f for f in os.listdir(train_path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
    val_images = len([f for f in os.listdir(val_path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
    
    print(f"✅ 找到 {train_images} 个训练图像")
    print(f"✅ 找到 {val_images} 个验证图像")
    
    # 检查标签目录
    train_labels = train_path.replace('images', 'labels')
    val_labels = val_path.replace('images', 'labels')
    
    if os.path.exists(train_labels):
        train_label_count = len([f for f in os.listdir(train_labels) if f.endswith('.txt')])
        print(f"✅ 找到 {train_label_count} 个训练标签")
    
    if os.path.exists(val_labels):
        val_label_count = len([f for f in os.listdir(val_labels) if f.endswith('.txt')])
        print(f"✅ 找到 {val_label_count} 个验证标签")


def main():
    """主训练函数"""
    args = parse_args()
    
    print("🚀 YOLOv12 RGB基线模型训练")
    print("纯RGB输入，用于与RGB+Depth模型对比")
    print("=" * 60)
    
    # 检查环境
    check_environment()
    print()
    
    # 检查数据集
    check_dataset_paths(args.data)
    print()
    
    try:
        # 创建模型
        if args.weights and os.path.exists(args.weights):
            print(f"📥 从预训练权重加载: {args.weights}")
            model = YOLO(args.weights)
            # 如果使用预训练权重，需要指定新的配置
            print(f"🔄 应用新配置: {args.cfg}")
        else:
            print(f"🏗️ 从配置文件创建模型: {args.cfg}")
            model = YOLO(args.cfg)
        
        # 训练参数
        train_args = {
            'data': args.data,
            'imgsz': args.imgsz,
            'epochs': args.epochs,
            'batch': args.batch_size,
            'device': args.device,
            'workers': args.workers,
            'project': args.project,
            'name': args.name,
            'save_period': args.save_period,
            'cache': args.cache,
            'verbose': True,
            'plots': True,  # 生成训练图表
            'patience': 50,  # 早停耐心值
            'amp': True,  # 自动混合精度
            'fraction': 1.0,  # 使用全部数据
            'profile': False,  # 关闭性能分析以节省内存
        }
        
        if args.resume:
            train_args['resume'] = args.resume
            
        # 开始训练
        print("🔥 开始RGB基线模型训练...")
        results = model.train(**train_args)
        
        print("=" * 60)
        print("🎉 RGB基线模型训练完成!")
        print(f"📁 结果保存在: {args.project}/{args.name}")
        if hasattr(model, 'trainer'):
            print(f"🏆 最佳模型: {model.trainer.best}")
            print(f"📄 最后模型: {model.trainer.last}")
        print("=" * 60)
        
        return results
        
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == '__main__':
    main()
