# Stage1 RGB+Depth Dataset Extension
# 扩展YOLODataset以支持RGB+Depth双模态输入

import os
import cv2
import numpy as np
import torch
from pathlib import Path
from copy import deepcopy
from ultralytics.data.dataset import YOLODataset
from ultralytics.data.rgbd_augment import RGBDTransforms, RGBDVisualization


class YOLORGBDDataset(YOLODataset):
    """
    阶段1：RGB+Depth双模态数据集
    扩展YOLODataset以支持RGB图像和深度图像的同时加载
    
    预期数据格式:
    dataset/
    ├── images/
    │   ├── train/
    │   │   ├── image1.jpg (RGB图像)
    │   │   └── image2.jpg
    │   └── val/
    └── depth/
        ├── train/
        │   ├── image1.png (对应的深度图，单通道)
        │   └── image2.png
        └── val/
    """

    def __init__(self, *args, depth_path=None, data=None, task="detect",
                 depth_format="auto", depth_scale=1.0, max_depth=80.0, **kwargs):
        """
        初始化RGB+Depth数据集

        Args:
            depth_path (str): 深度图像根目录路径
            data (dict): 数据集配置字典
            task (str): 任务类型，默认"detect"
            depth_format (str): 深度图格式 ("auto", "uint8", "uint16", "float32")
            depth_scale (float): 深度缩放因子，默认1.0
            max_depth (float): 最大深度值（米），用于归一化，默认80.0
            *args, **kwargs: 传递给父类YOLODataset的参数
        """
        # 保存深度相关参数
        self.depth_path = depth_path
        self.depth_format = depth_format
        self.depth_scale = depth_scale
        self.max_depth = max_depth

        # 从data配置中获取深度参数（如果有的话）
        if data:
            self.depth_format = data.get('depth_format', depth_format)
            self.depth_scale = data.get('depth_scale', depth_scale)
            self.max_depth = data.get('max_depth', max_depth)

        # 初始化父类
        super().__init__(*args, data=data, task=task, **kwargs)

        # 验证深度路径
        if depth_path and not os.path.exists(depth_path):
            print(f"警告: 深度图路径不存在: {depth_path}")

        # 为每个图像文件找到对应的深度文件
        self.depth_files = self._get_depth_files() if depth_path else None

        print(f"📊 深度图配置: 格式={self.depth_format}, 缩放={self.depth_scale}, 最大深度={self.max_depth}m")

    def _get_depth_files(self):
        """
        为每个RGB图像找到对应的深度图像文件
        适配KITTI数据集结构
        
        Returns:
            list: 深度图像文件路径列表，与self.im_files对应
        """
        depth_files = []
        
        for img_file in self.im_files:
            # KITTI结构: /path/kitti/images/train_set/xxx.jpg -> /path/kitti/depths/train_set/xxx.png
            img_path = Path(img_file)
            
            # 获取相对于images目录的路径
            # 例如: images/train_set/000001.jpg -> train_set/000001.jpg
            try:
                # 找到images在路径中的位置
                parts = img_path.parts
                images_idx = None
                for i, part in enumerate(parts):
                    if part == 'images':
                        images_idx = i
                        break
                
                if images_idx is not None:
                    # 构建depths路径
                    relative_path = Path(*parts[images_idx + 1:])  # train_set/xxx.jpg
                    # 将.jpg改为.png (深度图通常是PNG格式)
                    depth_relative = relative_path.with_suffix('.png')
                    depth_file = Path(self.depth_path) / depth_relative
                else:
                    # 如果找不到images目录，尝试直接替换
                    depth_file = Path(str(img_file).replace('/images/', '/depths/')).with_suffix('.png')
                
                if depth_file.exists():
                    depth_files.append(str(depth_file))
                else:
                    # 如果找不到深度图，使用None占位
                    depth_files.append(None)
                    if len(depth_files) <= 5:  # 只打印前几个警告
                        print(f"警告: 未找到深度图 {depth_file}")
                        
            except Exception as e:
                print(f"处理深度图路径时出错 {img_file}: {e}")
                depth_files.append(None)
        
        found_depth = sum(1 for d in depth_files if d is not None)
        print(f"找到 {found_depth}/{len(depth_files)} 个深度图像")
        
        return depth_files

    def _load_depth_image(self, depth_path, target_shape):
        """
        加载并预处理深度图像

        Args:
            depth_path (str): 深度图路径
            target_shape (tuple): 目标形状 (H, W)

        Returns:
            np.ndarray: 预处理后的深度图 [H, W, 1]，uint8格式
        """
        try:
            # 首先尝试以16位读取（KITTI深度图通常是16位PNG）
            depth_img = cv2.imread(depth_path, cv2.IMREAD_ANYDEPTH)

            if depth_img is None:
                # 如果16位读取失败，尝试8位灰度读取
                depth_img = cv2.imread(depth_path, cv2.IMREAD_GRAYSCALE)

            if depth_img is not None:
                # 调整尺寸
                if depth_img.shape[:2] != target_shape:
                    depth_img = cv2.resize(depth_img, (target_shape[1], target_shape[0]),
                                         interpolation=cv2.INTER_NEAREST)  # 使用最近邻插值保持深度值

                # 深度图预处理
                depth_img = self._preprocess_depth(depth_img)

                # 确保是3维 [H, W, 1]
                if depth_img.ndim == 2:
                    depth_img = np.expand_dims(depth_img, axis=2)

                return depth_img
            else:
                print(f"警告: 无法加载深度图 {depth_path}")
                return np.zeros((target_shape[0], target_shape[1], 1), dtype=np.uint8)

        except Exception as e:
            print(f"加载深度图时出错 {depth_path}: {e}")
            return np.zeros((target_shape[0], target_shape[1], 1), dtype=np.uint8)

    def _preprocess_depth(self, depth_img):
        """
        预处理深度图像，针对已经是uint8格式的KITTI深度图优化

        Args:
            depth_img (np.ndarray): 原始深度图

        Returns:
            np.ndarray: 预处理后的深度图，uint8格式
        """
        dtype = depth_img.dtype

        # 如果已经是uint8格式且在0-255范围内，说明已经预处理过
        if dtype == np.uint8 and depth_img.max() <= 255:
            # 只在第一次检测时显示日志，避免过多输出
            if not hasattr(self, '_uint8_depth_logged'):
                print(f"🔍 检测到uint8深度图，范围[{depth_img.min()}, {depth_img.max()}]，使用{self.depth_format}策略")
                self._uint8_depth_logged = True

            # 对于已经是uint8的深度图，我们假设它已经正确归一化
            # 只需要确保数据质量和一致性

            # 转换为float32进行处理
            depth_img = depth_img.astype(np.float32)

            # 应用深度缩放（如果需要）
            if self.depth_scale != 1.0:
                depth_img = depth_img * self.depth_scale

            # 移除无效深度值
            valid_mask = (depth_img > 0) & np.isfinite(depth_img)

            if not valid_mask.any():
                return np.zeros_like(depth_img, dtype=np.uint8)

            # 对于uint8深度图，我们有几种处理策略：

            # 策略1: 直接使用（假设已经正确归一化到深度范围）
            if self.depth_format == "uint8_direct":
                # 直接使用，只做基本的数值范围检查
                depth_img = np.clip(depth_img, 0, 255)
                return depth_img.astype(np.uint8)

            # 策略2: 假设是归一化的深度值，重新映射到物理深度
            elif self.depth_format == "uint8_normalized" or self.depth_format == "auto":
                # 假设255对应最大深度，0对应无效/最近距离
                # 这是最常见的uint8深度图编码方式

                # 将0-255映射到0-max_depth的深度范围
                # 但保持0值为无效深度
                depth_physical = depth_img.copy()
                depth_physical[valid_mask] = (depth_img[valid_mask] / 255.0) * self.max_depth
                depth_physical[~valid_mask] = 0

                # 重新归一化到0-255用于网络输入
                if self.max_depth > 0:
                    depth_normalized = depth_physical.copy()
                    depth_normalized[valid_mask] = (depth_physical[valid_mask] / self.max_depth * 255)
                    depth_normalized[~valid_mask] = 0
                    return depth_normalized.astype(np.uint8)
                else:
                    return depth_img.astype(np.uint8)

            # 策略3: 反向深度编码（255=近，1=远）
            elif self.depth_format == "uint8_inverse":
                # 有些深度图使用反向编码
                depth_img[valid_mask] = 255 - depth_img[valid_mask]
                return depth_img.astype(np.uint8)

            else:
                # 默认：直接使用原始值
                return np.clip(depth_img, 0, 255).astype(np.uint8)

        else:
            # 处理其他格式（uint16, float32等）
            depth_img = depth_img.astype(np.float32)

            # 应用深度缩放
            if self.depth_scale != 1.0:
                depth_img = depth_img * self.depth_scale

            # 移除无效深度值
            valid_mask = (depth_img > 0) & np.isfinite(depth_img)

            if not valid_mask.any():
                return np.zeros_like(depth_img, dtype=np.uint8)

            # 根据深度格式进行处理
            if self.depth_format == "uint16" or (self.depth_format == "auto" and dtype == np.uint16):
                # 标准KITTI 16位深度图处理
                if depth_img[valid_mask].max() > 1000:  # 毫米单位
                    depth_img = depth_img / 1000.0  # 转换为米

            elif self.depth_format == "float32" or (self.depth_format == "auto" and dtype in [np.float32, np.float64]):
                # 浮点深度图，通常已经是米单位
                pass

            # 裁剪到合理的深度范围
            depth_img = np.clip(depth_img, 0, self.max_depth)

            # 归一化到0-255
            if self.max_depth > 0:
                depth_img[valid_mask] = (depth_img[valid_mask] / self.max_depth * 255)
                depth_img[~valid_mask] = 0

            return depth_img.astype(np.uint8)

    def load_image(self, i, rect_mode=False):
        """
        加载RGB图像和对应的深度图像
        
        Args:
            i (int): 图像索引
            rect_mode (bool): 是否使用矩形模式
            
        Returns:
            tuple: (combined_image, hw_original, hw_resized) 
                   combined_image: 4通道图像 [H, W, 4] (RGB + Depth)
                   hw_original: 原始图像尺寸
                   hw_resized: 调整后的图像尺寸
        """
        # 加载RGB图像 - BaseDataset.load_image()返回(im, hw_original, hw_resized)
        rgb_img, hw_original, hw_resized = super().load_image(i, rect_mode)
        
        # 加载深度图像
        if self.depth_files and self.depth_files[i]:
            depth_img = self._load_depth_image(self.depth_files[i], rgb_img.shape[:2])
        else:
            # 如果没有深度图，创建零深度图
            depth_img = np.zeros((rgb_img.shape[0], rgb_img.shape[1], 1), dtype=np.uint8)
        
        # 合并RGB和Depth: [H, W, 3] + [H, W, 1] = [H, W, 4]
        combined_img = np.concatenate([rgb_img, depth_img], axis=2)
        
        return combined_img, hw_original, hw_resized

    def __getitem__(self, index):
        """
        获取数据样本
        
        Args:
            index (int): 样本索引
            
        Returns:
            dict: 包含4通道图像和标签的字典
        """
        return self.transforms(self.get_image_and_label(index))

    def get_image_and_label(self, index):
        """
        获取图像和标签数据
        
        Args:
            index (int): 样本索引
            
        Returns:
            dict: 包含图像和标签信息的字典
        """
        label = deepcopy(self.labels[index])  # 深拷贝标签以避免修改原始数据
        label["img"], label["ori_shape"], label["resized_shape"] = self.load_image(index)
        label["ratio_pad"] = (
            label["resized_shape"][0] / label["ori_shape"][0],
            label["resized_shape"][1] / label["ori_shape"][1],
        )  # for evaluation
        
        if self.rect:
            label["rect_shape"] = self.batch_shapes[self.batch[index]]
        
        return self.update_labels_info(label)

    def build_transforms(self, hyp=None):
        """
        构建RGB+D专用的数据增强管道
        
        Args:
            hyp: 超参数配置
            
        Returns:
            RGBDTransforms: 自定义的4通道数据增强管道
        """
        return RGBDTransforms(
            imgsz=self.imgsz,
            augment=self.augment,
            hyp=hyp
        )

    @staticmethod
    def collate_fn(batch):
        """
        批处理函数，将多个样本合并为一个批次
        
        Args:
            batch (list): 样本列表
            
        Returns:
            dict: 批处理后的数据字典
        """
        new_batch = {}
        keys = batch[0].keys()
        values = list(zip(*[list(b.values()) for b in batch]))
        
        for i, k in enumerate(keys):
            value = values[i]
            if k == "img":
                # 确保图像是4通道的
                img_batch = []
                for img in value:
                    if img.shape[0] == 4:  # 已经是4通道
                        img_batch.append(img)
                    elif img.shape[0] == 3:  # 只有RGB，需要添加深度通道
                        depth_channel = torch.zeros(1, img.shape[1], img.shape[2], dtype=img.dtype, device=img.device)
                        img_4ch = torch.cat([img, depth_channel], dim=0)
                        img_batch.append(img_4ch)
                    else:
                        raise ValueError(f"Unexpected image channels: {img.shape[0]}")
                
                new_batch[k] = torch.stack(img_batch, 0)
            elif k in {"masks", "keypoints", "bboxes", "cls", "segments", "obb"}:
                new_batch[k] = torch.cat(value, 0)
            else:
                new_batch[k] = value
        
        # 处理批次索引
        new_batch["batch_idx"] = list(new_batch["batch_idx"])
        for i in range(len(new_batch["batch_idx"])):
            new_batch["batch_idx"][i] += i
        new_batch["batch_idx"] = torch.cat(new_batch["batch_idx"], 0)
        
        return new_batch