"""
YOLOv12 Stage1 双模态YOLO模型
扩展标准YOLO类以支持RGB+Depth训练
"""

from ultralytics.models.yolo.model import YOLO
from ultralytics.models.yolo.detect.rgbd_train import RGBDDetectionTrainer
from ultralytics.nn.tasks import DetectionModel
import yaml

# 导入并应用RGB+D可视化补丁
try:
    from ultralytics.data.rgbd_plotting_patch import apply_rgbd_visualization_patch
    apply_rgbd_visualization_patch()
except ImportError:
    print("警告: 无法导入RGB+D可视化补丁，可能会出现4通道图像显示问题")


class RGBDDetectionModel(DetectionModel):
    """扩展DetectionModel以支持强制scale参数"""
    
    def __init__(self, cfg="yolov8n.yaml", ch=3, nc=None, verbose=True, force_scale=None):
        # 如果传入了force_scale，先修改配置
        if force_scale and isinstance(cfg, str):
            with open(cfg, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            # 强制设置scale
            config['scale'] = force_scale
            super().__init__(cfg=config, ch=ch, nc=nc, verbose=verbose)
        else:
            super().__init__(cfg=cfg, ch=ch, nc=nc, verbose=verbose)


class YOLORGBD(YOLO):
    """
    扩展YOLO类以支持RGB+Depth双模态训练
    用于YOLOv12 Stage1消融实验
    """

    @property
    def task_map(self):
        """重写task_map使用自定义的RGBDDetectionTrainer和RGBDDetectionModel"""
        return {
            "detect": {
                "model": RGBDDetectionModel,
                "trainer": RGBDDetectionTrainer,  # 使用自定义训练器
                "validator": self._get_validator("detect"),
                "predictor": self._get_predictor("detect"),
            }
        }
    
    def __init__(self, model="yolo11n.pt", task=None, verbose=False, force_scale='m'):
        """初始化YOLORGBD模型，默认强制使用YOLOv12m规模"""
        self.force_scale = force_scale
        super().__init__(model=model, task=task, verbose=verbose)
    
    def _smart_load(self, key):
        """重写_smart_load以传入force_scale参数和4通道输入"""
        if key == "model":
            return lambda cfg, **kwargs: RGBDDetectionModel(cfg, ch=4, force_scale=self.force_scale, **kwargs)
        else:
            return super()._smart_load(key)
    
    def _get_validator(self, task):
        """获取RGB+D验证器"""
        from ultralytics.models.yolo.detect.rgbd_val import RGBDDetectionValidator
        return RGBDDetectionValidator
    
    def _get_predictor(self, task):
        """获取预测器"""  
        from ultralytics.models.yolo.detect import DetectionPredictor
        return DetectionPredictor