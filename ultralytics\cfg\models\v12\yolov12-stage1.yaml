# YOLOv12m-DualModal Stage1 🚀, AGPL-3.0 license
# YOLOv12m object detection model with RGB+Depth inputs and SimplifiedDepthExtractor
# 阶段1：基础双模态输入 + 简化深度特征提取器 (Medium规模)

# Parameters
nc: 3  # number of classes (KITTI: Car, <PERSON>ede<PERSON>rian, Cyclist)
ch: 4  # input channels (RGB=3 + Depth=1)
scale: 'm'  # 明确指定使用YOLOv12m规模

# Model scaling - 使用标准YOLOv12格式
scales: # model compound scaling constants
  # [depth, width, max_channels]  
  n: [0.50, 0.25, 1024]
  s: [0.50, 0.50, 1024] 
  m: [0.50, 1.00, 512]  # Medium规模：depth=0.5, width=1.0, max_ch=512
  l: [1.00, 1.00, 512]
  x: [1.00, 1.50, 512]

# 双模态Backbone
backbone:
  # [from, repeats, module, args]
  # 输入分离和RGB处理 
  - [-1, 1, InputSeparator, []] # 0-分离4通道输入为RGB和Depth
  - [-1, 1, Conv,  [64, 3, 2]]   # 1-RGB P1/2 (处理3通道RGB输出)
  - [-1, 1, Conv,  [128, 3, 2, 1, 2]]  # 2-RGB P2/4  
  - [-1, 2, C3k2,  [256, False, 0.25]] # 3-RGB 
  - [-1, 1, Conv,  [256, 3, 2, 1, 4]]  # 4-RGB P3/8
  - [-1, 2, C3k2,  [512, False, 0.25]] # 5-RGB 
  - [-1, 1, Conv,  [512, 3, 2]]  # 6-RGB P4/16
  - [-1, 4, A2C2f, [512, True, 4]] # 7-RGB 
  - [-1, 1, Conv,  [1024, 3, 2]] # 8-RGB P5/32
  - [-1, 4, A2C2f, [1024, True, 1]] # 9-RGB 
  
  # 深度处理分支 (SimplifiedDepthExtractor)
  - [0, 1, DepthGetter, [0]]  # 10-获取Depth输出（从InputSeparator索引0获取）
  - [-1, 1, DepthConv, [32, 3, 2]] # 11-Depth P1/2 (轻量级深度特征)
  - [-1, 1, DepthConv, [64, 3, 2]] # 12-Depth P2/4
  - [-1, 1, DepthConv, [128, 3, 2]] # 13-Depth P3/8
  - [-1, 1, DepthConv, [256, 3, 2]] # 14-Depth P4/16
  - [-1, 1, DepthConv, [512, 3, 2]] # 15-Depth P5/32

# 双模态Head 
head:
  # 特征融合层
  - [[9, 15], 1, Concat, [1]]  # 16-融合RGB P5和Depth P5
  
  # FPN上采样路径
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]] # 17-上采样
  - [[7, 14], 1, Concat, [1]]  # 18-融合RGB P4和Depth P4
  - [[-1, 17], 1, Concat, [1]] # 19-连接上采样特征
  - [-1, 2, A2C2f, [512, False, -1]] # 20-P4融合处理

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]] # 21-上采样  
  - [[5, 13], 1, Concat, [1]]  # 22-融合RGB P3和Depth P3
  - [[-1, 21], 1, Concat, [1]] # 23-连接上采样特征
  - [-1, 2, A2C2f, [256, False, -1]] # 24-P3融合处理 (最终P3)

  # PAN下采样路径
  - [-1, 1, Conv, [256, 3, 2]]    # 25-下采样
  - [[-1, 20], 1, Concat, [1]]    # 26-连接P4特征
  - [-1, 2, A2C2f, [512, False, -1]] # 27-P4最终特征

  - [-1, 1, Conv, [512, 3, 2]]    # 28-下采样
  - [[-1, 16], 1, Concat, [1]]    # 29-连接P5特征
  - [-1, 2, C3k2, [1024, True]]   # 30-P5最终特征

  # 检测头
  - [[24, 27, 30], 1, Detect, [nc]] # 31-Detect(P3, P4, P5)