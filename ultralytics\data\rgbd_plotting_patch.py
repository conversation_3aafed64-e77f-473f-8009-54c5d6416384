# RGB+D 可视化补丁
# 修复4通道图像的可视化问题

import numpy as np
import torch
from ultralytics.utils import plotting
from ultralytics.data.rgbd_augment import RGBDVisualization

# 保存原始的plot_images函数
_original_plot_images = plotting.plot_images

def plot_images_rgbd_patch(images, batch_idx, cls, bboxes=None, masks=None, kpts=None, fname="images.jpg", names=None, on_plot=None):
    """
    RGB+D兼容的图像可视化函数
    自动检测4通道图像并只显示RGB部分
    """
    # 检查是否为4通道图像
    if isinstance(images, (list, tuple)) and len(images) > 0:
        first_img = images[0]
        if isinstance(first_img, torch.Tensor) and first_img.shape[0] == 4:
            # 提取RGB通道用于可视化
            images_rgb = [RGBDVisualization.extract_rgb_for_visualization(img) for img in images]
            return _original_plot_images(images_rgb, batch_idx, cls, bboxes, masks, kpts, fname, names, on_plot)
        elif isinstance(first_img, np.ndarray) and first_img.shape[-1] == 4:
            # 提取RGB通道用于可视化
            images_rgb = [RGBDVisualization.extract_rgb_for_visualization(img) for img in images]
            return _original_plot_images(images_rgb, batch_idx, cls, bboxes, masks, kpts, fname, names, on_plot)
    elif isinstance(images, torch.Tensor) and len(images.shape) == 4 and images.shape[1] == 4:  # batch tensor [B, 4, H, W]
        # 提取RGB通道
        images_rgb = images[:, :3, :, :]  # 取前3个通道
        return _original_plot_images(images_rgb, batch_idx, cls, bboxes, masks, kpts, fname, names, on_plot)
    elif isinstance(images, np.ndarray) and len(images.shape) == 4 and images.shape[1] == 4:  # batch array [B, 4, H, W]
        # 提取RGB通道
        images_rgb = images[:, :3, :, :]  # 取前3个通道
        return _original_plot_images(images_rgb, batch_idx, cls, bboxes, masks, kpts, fname, names, on_plot)
    elif isinstance(images, np.ndarray) and len(images.shape) == 4 and images.shape[-1] == 4:  # batch array [B, H, W, 4]
        # 提取RGB通道
        images_rgb = images[:, :, :, :3]  # 取前3个通道
        return _original_plot_images(images_rgb, batch_idx, cls, bboxes, masks, kpts, fname, names, on_plot)

    # 如果不是4通道，使用原始函数
    return _original_plot_images(images, batch_idx, cls, bboxes, masks, kpts, fname, names, on_plot)

# 应用补丁
def apply_rgbd_visualization_patch():
    """
    应用RGB+D可视化补丁，修复4通道图像显示问题
    """
    plotting.plot_images = plot_images_rgbd_patch
    print("✅ RGB+D可视化补丁已应用")

# 移除补丁
def remove_rgbd_visualization_patch():
    """
    移除RGB+D可视化补丁，恢复原始函数
    """
    plotting.plot_images = _original_plot_images
    print("✅ RGB+D可视化补丁已移除")

# 自动应用补丁
apply_rgbd_visualization_patch()