#!/usr/bin/env python3
"""
测试阶段2模块功能
验证多尺度深度融合模块是否正常工作
"""

import sys
from pathlib import Path
import torch
import torch.nn as nn

# 添加项目根目录到系统路径
ROOT = Path(__file__).resolve().parent
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

from ultralytics.nn.modules.stage2_fusion import (
    MultiScaleFusion,
    ChannelAttention,
    AdaptiveFusion,
    CrossScaleFusion,
    Stage2FusionBlock
)
from ultralytics.models.yolo.rgbd_model import YOLORGBD


def test_multi_scale_fusion():
    """测试多尺度融合模块"""
    print("🧪 测试多尺度融合模块...")
    
    # 创建测试数据
    batch_size = 2
    rgb_channels = 256
    depth_channels = 128
    height, width = 32, 32
    
    rgb_feat = torch.randn(batch_size, rgb_channels, height, width)
    depth_feat = torch.randn(batch_size, depth_channels, height, width)
    
    print(f"  输入RGB特征: {rgb_feat.shape}")
    print(f"  输入深度特征: {depth_feat.shape}")
    
    # 测试多尺度融合
    msf = MultiScaleFusion(rgb_channels, depth_channels, fusion_channels=256)
    
    # 前向传播
    with torch.no_grad():
        fused_feat = msf(rgb_feat, depth_feat)
    
    print(f"  融合后特征: {fused_feat.shape}")
    
    # 检查输出
    assert fused_feat.shape == (batch_size, 256, height, width), f"输出形状错误: {fused_feat.shape}"
    assert not torch.isnan(fused_feat).any(), "输出包含NaN"
    assert torch.isfinite(fused_feat).all(), "输出包含无穷大"
    
    print("  ✅ 多尺度融合测试通过")
    
    return msf, fused_feat


def test_channel_attention():
    """测试通道注意力模块"""
    print("\n🧪 测试通道注意力模块...")
    
    # 创建测试数据
    batch_size = 2
    in_channels = 512
    out_channels = 256
    height, width = 16, 16
    
    input_feat = torch.randn(batch_size, in_channels, height, width)
    print(f"  输入特征: {input_feat.shape}")
    
    # 测试通道注意力
    ca = ChannelAttention(in_channels, out_channels)
    
    # 前向传播
    with torch.no_grad():
        attention_weights = ca(input_feat)
    
    print(f"  注意力权重: {attention_weights.shape}")
    
    # 检查输出
    expected_shape = (batch_size, out_channels * 2, 1, 1)
    assert attention_weights.shape == expected_shape, f"注意力权重形状错误: {attention_weights.shape}"
    assert (attention_weights >= 0).all() and (attention_weights <= 1).all(), "注意力权重应在[0,1]范围内"
    
    print("  ✅ 通道注意力测试通过")
    
    return ca, attention_weights


def test_adaptive_fusion():
    """测试自适应融合模块"""
    print("\n🧪 测试自适应融合模块...")
    
    # 创建测试数据
    batch_size = 2
    channels = 256
    height, width = 32, 32
    
    rgb_feat = torch.randn(batch_size, channels, height, width)
    depth_feat = torch.randn(batch_size, channels, height, width)
    
    print(f"  RGB特征: {rgb_feat.shape}")
    print(f"  深度特征: {depth_feat.shape}")
    
    # 测试自适应融合
    af = AdaptiveFusion(channels)
    
    # 前向传播
    with torch.no_grad():
        adaptive_fused = af(rgb_feat, depth_feat)
    
    print(f"  自适应融合结果: {adaptive_fused.shape}")
    
    # 检查输出
    assert adaptive_fused.shape == rgb_feat.shape, f"输出形状错误: {adaptive_fused.shape}"
    assert not torch.isnan(adaptive_fused).any(), "输出包含NaN"
    
    print("  ✅ 自适应融合测试通过")
    
    return af, adaptive_fused


def test_cross_scale_fusion():
    """测试跨尺度融合模块"""
    print("\n🧪 测试跨尺度融合模块...")
    
    # 创建不同尺度的测试数据
    batch_size = 2
    channels_list = [256, 512, 1024]
    
    features = [
        torch.randn(batch_size, 256, 64, 64),   # P3
        torch.randn(batch_size, 512, 32, 32),   # P4
        torch.randn(batch_size, 1024, 16, 16)   # P5
    ]
    
    print(f"  输入特征尺度:")
    for i, feat in enumerate(features):
        print(f"    P{i+3}: {feat.shape}")
    
    # 测试跨尺度融合
    csf = CrossScaleFusion(channels_list)
    
    # 前向传播
    with torch.no_grad():
        cross_fused = csf(features)
    
    print(f"  跨尺度融合结果:")
    for i, feat in enumerate(cross_fused):
        print(f"    P{i+3}: {feat.shape}")
    
    # 检查输出
    assert len(cross_fused) == len(features), "输出特征数量错误"
    for i, (orig, fused) in enumerate(zip(features, cross_fused)):
        assert fused.shape == orig.shape, f"P{i+3}输出形状错误: {fused.shape} vs {orig.shape}"
        assert not torch.isnan(fused).any(), f"P{i+3}输出包含NaN"
    
    print("  ✅ 跨尺度融合测试通过")
    
    return csf, cross_fused


def test_stage2_fusion_block():
    """测试阶段2完整融合块"""
    print("\n🧪 测试阶段2完整融合块...")
    
    # 测试多尺度融合类型
    print("  测试多尺度融合类型...")
    rgb_feat = torch.randn(2, 256, 32, 32)
    depth_feat = torch.randn(2, 128, 32, 32)
    
    fusion_block = Stage2FusionBlock(256, 128, fusion_type='multi_scale')
    
    with torch.no_grad():
        ms_result = fusion_block(rgb_feat, depth_feat)
    
    print(f"    多尺度融合结果: {ms_result.shape}")
    assert ms_result.shape[0] == 2 and ms_result.shape[2:] == (32, 32), "多尺度融合输出形状错误"
    
    # 测试自适应融合类型
    print("  测试自适应融合类型...")
    depth_feat_aligned = torch.randn(2, 256, 32, 32)  # 通道对齐
    
    adaptive_block = Stage2FusionBlock(256, 256, fusion_type='adaptive')
    
    with torch.no_grad():
        adaptive_result = adaptive_block(rgb_feat, depth_feat_aligned)
    
    print(f"    自适应融合结果: {adaptive_result.shape}")
    assert adaptive_result.shape == rgb_feat.shape, "自适应融合输出形状错误"
    
    print("  ✅ 阶段2融合块测试通过")
    
    return fusion_block, ms_result


def test_stage2_model():
    """测试完整的阶段2模型"""
    print("\n🧪 测试完整的阶段2模型...")

    try:
        # 创建阶段2模型 - 显式指定4通道输入
        print("  创建阶段2模型...")
        model = YOLORGBD('ultralytics/cfg/models/v12/yolov12-stage2.yaml')

        # 确保模型使用4通道输入
        if hasattr(model.model, 'yaml') and 'ch' in model.model.yaml:
            model.model.yaml['ch'] = 4

        model.eval()

        # 创建测试输入 - 4通道 (RGB + Depth)
        batch_size = 1
        test_input = torch.randn(batch_size, 4, 640, 640)  # 4通道输入

        print(f"  模型输入: {test_input.shape}")
        print(f"  模型期望通道数: 4 (RGB + Depth)")
        
        # 前向传播
        with torch.no_grad():
            outputs = model.model(test_input)
        
        print(f"  模型输出:")
        if isinstance(outputs, (list, tuple)):
            for i, output in enumerate(outputs):
                if hasattr(output, 'shape'):
                    print(f"    输出{i}: {output.shape}")
                else:
                    print(f"    输出{i}: {type(output)}")
        else:
            print(f"    输出: {outputs.shape if hasattr(outputs, 'shape') else type(outputs)}")
        
        # 计算参数量
        total_params = sum(p.numel() for p in model.model.parameters())
        trainable_params = sum(p.numel() for p in model.model.parameters() if p.requires_grad)
        
        print(f"  模型参数:")
        print(f"    总参数量: {total_params:,} ({total_params/1e6:.2f}M)")
        print(f"    可训练参数: {trainable_params:,} ({trainable_params/1e6:.2f}M)")
        
        print("  ✅ 阶段2模型测试通过")
        
        return model, outputs
        
    except Exception as e:
        print(f"  ❌ 阶段2模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None


def benchmark_fusion_modules():
    """性能基准测试"""
    print("\n⏱️ 融合模块性能基准测试...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"  测试设备: {device}")
    
    # 测试数据
    rgb_feat = torch.randn(4, 256, 32, 32).to(device)
    depth_feat = torch.randn(4, 128, 32, 32).to(device)
    
    # 测试多尺度融合性能
    msf = MultiScaleFusion(256, 128, 256).to(device)
    msf.eval()
    
    # 预热
    for _ in range(10):
        with torch.no_grad():
            _ = msf(rgb_feat, depth_feat)
    
    # 计时
    if device.type == 'cuda':
        torch.cuda.synchronize()
        start_event = torch.cuda.Event(enable_timing=True)
        end_event = torch.cuda.Event(enable_timing=True)
        
        start_event.record()
        for _ in range(100):
            with torch.no_grad():
                _ = msf(rgb_feat, depth_feat)
        end_event.record()
        torch.cuda.synchronize()
        
        elapsed_time = start_event.elapsed_time(end_event) / 100
        print(f"  多尺度融合平均耗时: {elapsed_time:.2f}ms")
    else:
        import time
        start_time = time.time()
        for _ in range(100):
            with torch.no_grad():
                _ = msf(rgb_feat, depth_feat)
        elapsed_time = (time.time() - start_time) * 1000 / 100
        print(f"  多尺度融合平均耗时: {elapsed_time:.2f}ms")


def main():
    """主测试函数"""
    print("🚀 阶段2模块测试")
    print("="*60)
    
    try:
        # 测试各个模块
        test_multi_scale_fusion()
        test_channel_attention()
        test_adaptive_fusion()
        test_cross_scale_fusion()
        test_stage2_fusion_block()
        test_stage2_model()
        
        # 性能测试
        benchmark_fusion_modules()
        
        print(f"\n🎉 所有测试通过!")
        print(f"💡 阶段2模块已准备就绪，可以开始训练")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
