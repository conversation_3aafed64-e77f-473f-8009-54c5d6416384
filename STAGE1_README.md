# YOLOv12 Stage1: 双模态输入实现

## 概述

阶段1实现了YOLOv12的基础双模态输入支持，通过SimplifiedDepthExtractor模块处理RGB+Depth输入。这是6阶段消融实验的第一阶段。

## 已实现功能

### 1. 核心模块
- **InputSeparator**: 分离4通道输入为RGB(3通道)和Depth(1通道)
- **DepthConv**: 轻量级深度特征提取卷积块
- **DepthGetter**: 从InputSeparator获取深度输出的辅助模块

### 2. 数据处理
- **YOLORGBDDataset**: 扩展YOLODataset支持RGB+Depth双模态数据加载
- 支持自动匹配RGB图像和对应的深度图像
- 处理深度图归一化和尺寸调整

### 3. 模型配置
- **yolov12-stage1.yaml**: 阶段1模型配置文件
- 支持4通道输入（RGB + Depth）
- 双分支特征提取和融合

### 4. 训练工具
- **train_stage1.py**: 专用训练脚本
- **test_stage1_model.py**: 模型测试验证脚本
- **coco-stage1.yaml**: 数据集配置模板

## 文件结构

```
yolov12-main/
├── ultralytics/
│   ├── cfg/models/v12/
│   │   └── yolov12-stage1.yaml          # 阶段1模型配置
│   ├── data/
│   │   └── rgbd_dataset.py              # RGB+Depth数据集
│   └── nn/modules/
│       ├── block.py                     # 新增双模态模块
│       ├── __init__.py                  # 模块导入更新
│       └── tasks.py                     # 导入更新
├── datasets/
│   └── coco-stage1.yaml                 # 数据集配置
├── design_docs/
│   └── stage1_architecture.md          # 架构设计文档
├── train_stage1.py                     # 训练脚本
└── test_stage1_model.py                # 测试脚本
```

## 使用方法

### 1. 数据准备

准备数据集结构如下：
```
dataset/
├── images/
│   ├── train/
│   │   ├── image1.jpg
│   │   └── image2.jpg
│   └── val/
│       ├── image1.jpg
│       └── image2.jpg
├── depth/
│   ├── train/
│   │   ├── image1.png
│   │   └── image2.png
│   └── val/
│       ├── image1.png
│       └── image2.png
└── labels/
    ├── train/
    │   ├── image1.txt
    │   └── image2.txt
    └── val/
        ├── image1.txt
        └── image2.txt
```

### 2. 配置数据集

编辑 `datasets/coco-stage1.yaml`:
```yaml
path: /path/to/your/dataset
train: images/train
val: images/val
depth_path: /path/to/your/dataset/depth
nc: 80
names: [...]  # 类别名称
```

### 3. 模型测试

验证模型配置是否正确：
```bash
python test_stage1_model.py
```

### 4. 开始训练

使用训练脚本启动阶段1训练：
```bash
python train_stage1.py \
    --data datasets/coco-stage1.yaml \
    --cfg ultralytics/cfg/models/v12/yolov12-stage1.yaml \
    --epochs 100 \
    --batch-size 16 \
    --imgsz 640 \
    --name yolov12-stage1-exp1
```

## 架构设计

### 输入处理流程
```
4通道输入 [B, 4, H, W]
    ↓
InputSeparator
    ↓
RGB [B, 3, H, W]  +  Depth [B, 1, H, W]
    ↓                     ↓
RGB Backbone          Depth Branch
(标准YOLOv12)         (SimplifiedDepthExtractor)
    ↓                     ↓
RGB特征金字塔          Depth特征金字塔
    ↓                     ↓
        特征融合 (Concat)
              ↓
          检测头输出
```

### 特征融合策略
- **P3层**: RGB(512) + Depth(128) → 640通道
- **P4层**: RGB(512) + Depth(256) → 768通道  
- **P5层**: RGB(1024) + Depth(512) → 1536通道

## ⚠️ 当前限制

1. **parse_model函数**: 需要修改以正确处理DepthGetter模块与InputSeparator的链接
2. **训练器兼容性**: 可能需要自定义训练器以使用YOLORGBDDataset
3. **模型加载**: 当前的YOLO类可能需要修改以支持4通道输入

## 下一步工作

1. 修改parse_model函数处理DepthGetter链接
2. 创建自定义训练器支持RGB+Depth数据
3. 测试完整训练流程
4. 准备基线性能评估

## 预期效果

- 增加约20-30%的参数量（相比纯RGB YOLOv12m）
- 提供基础的深度信息处理能力
- 为后续阶段（多尺度融合、注意力机制等）打下基础
- 验证双模态输入的可行性

## 消融实验追踪

- **阶段0** (基线): 标准YOLOv12m → 获得基准mAP
- **阶段1** (当前): 基线 + SimplifiedDepthExtractor → 预期mAP提升1-3%