#!/usr/bin/env python3
"""
阶段2训练启动脚本
配置: 300 epochs, 8 workers, batch_size=16
"""

import sys
from pathlib import Path
import torch
import time
import psutil
import os

# 添加项目根目录到系统路径
ROOT = Path(__file__).resolve().parent
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))


def check_system_resources():
    """检查系统资源"""
    print("🔍 系统资源检查")
    print("="*50)
    
    # CPU信息
    cpu_count = psutil.cpu_count()
    cpu_percent = psutil.cpu_percent(interval=1)
    print(f"💻 CPU: {cpu_count}核心, 当前使用率: {cpu_percent}%")
    
    # 内存信息
    memory = psutil.virtual_memory()
    print(f"💾 内存: {memory.total/1e9:.1f}GB总量, {memory.available/1e9:.1f}GB可用")
    
    # GPU信息
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1e9
            print(f"🖥️ GPU {i}: {gpu_name}, {gpu_memory:.1f}GB显存")
    else:
        print("⚠️ 未检测到GPU")
    
    # 磁盘空间
    disk_usage = psutil.disk_usage('.')
    print(f"💿 磁盘: {disk_usage.free/1e9:.1f}GB可用空间")
    
    print()


def check_dataset():
    """检查数据集"""
    print("📊 数据集检查")
    print("="*50)
    
    dataset_config = "datasets/kitti-stage1-uint8-depth.yaml"
    
    if Path(dataset_config).exists():
        print(f"✅ 数据集配置: {dataset_config}")
        
        # 检查本地数据集路径
        local_dataset = Path("D:/Program_Files/Pycharm_2024.1.7/Pycharm_Projects/dataset/kitti")
        if local_dataset.exists():
            print(f"✅ 本地数据集: {local_dataset}")
            
            # 统计数据集文件
            train_images = list(local_dataset.glob("**/train/**/*.png"))
            val_images = list(local_dataset.glob("**/val/**/*.png"))
            
            print(f"📈 训练图像: {len(train_images)} 张")
            print(f"📊 验证图像: {len(val_images)} 张")
        else:
            print(f"⚠️ 本地数据集路径不存在: {local_dataset}")
    else:
        print(f"❌ 数据集配置不存在: {dataset_config}")
    
    print()


def check_stage1_weights():
    """检查阶段1权重"""
    print("🏋️ 阶段1权重检查")
    print("="*50)
    
    stage1_weights = "best.pt"
    
    if Path(stage1_weights).exists():
        file_size = Path(stage1_weights).stat().st_size / 1e6
        print(f"✅ 阶段1权重: {stage1_weights} ({file_size:.1f}MB)")
        
        # 尝试加载权重验证
        try:
            checkpoint = torch.load(stage1_weights, map_location='cpu')
            if 'model' in checkpoint:
                print(f"✅ 权重格式: 完整训练检查点")
                if 'epoch' in checkpoint:
                    print(f"📊 训练轮数: {checkpoint['epoch']}")
                if 'best_fitness' in checkpoint:
                    print(f"📈 最佳性能: {checkpoint['best_fitness']:.4f}")
            else:
                print(f"✅ 权重格式: 纯模型权重")
        except Exception as e:
            print(f"⚠️ 权重加载警告: {e}")
    else:
        print(f"❌ 阶段1权重不存在: {stage1_weights}")
        print("请先完成阶段1训练或提供权重文件")
    
    print()


def optimize_training_environment():
    """优化训练环境"""
    print("⚙️ 训练环境优化")
    print("="*50)
    
    # 设置环境变量
    os.environ['CUDA_LAUNCH_BLOCKING'] = '0'  # 异步CUDA启动
    os.environ['TORCH_CUDNN_V8_API_ENABLED'] = '1'  # 启用cuDNN v8
    
    # PyTorch优化
    if torch.cuda.is_available():
        torch.backends.cudnn.benchmark = True  # 优化cuDNN性能
        torch.backends.cudnn.deterministic = False  # 允许非确定性算法
        print("✅ GPU优化: cuDNN benchmark启用")
    
    # 设置线程数
    torch.set_num_threads(8)  # 匹配workers数量
    print(f"✅ CPU线程: {torch.get_num_threads()}")
    
    print()


def estimate_training_time():
    """估算训练时间"""
    print("⏱️ 训练时间估算")
    print("="*50)
    
    # 基于阶段2测试结果估算
    inference_time_ms = 4.0  # 每个batch的推理时间
    batch_size = 16
    epochs = 300
    
    # 假设训练集大小（KITTI约7481张图像）
    estimated_train_samples = 7481
    batches_per_epoch = estimated_train_samples // batch_size
    
    # 训练时间估算（包括反向传播，约为推理时间的3倍）
    training_time_per_batch = inference_time_ms * 3 / 1000  # 秒
    time_per_epoch = batches_per_epoch * training_time_per_batch
    total_training_time = time_per_epoch * epochs
    
    print(f"📊 估算参数:")
    print(f"  每轮批次数: {batches_per_epoch}")
    print(f"  每批次时间: {training_time_per_batch:.2f}秒")
    print(f"  每轮时间: {time_per_epoch/60:.1f}分钟")
    print(f"  总训练时间: {total_training_time/3600:.1f}小时")
    
    # 验证时间
    val_time_per_epoch = time_per_epoch * 0.2  # 验证约为训练的20%
    total_val_time = val_time_per_epoch * epochs
    
    print(f"  验证时间: {total_val_time/3600:.1f}小时")
    print(f"  总计时间: {(total_training_time + total_val_time)/3600:.1f}小时")
    
    print()


def create_training_script():
    """创建优化的训练脚本"""
    print("📝 生成训练命令")
    print("="*50)
    
    training_command = """
# 阶段2训练命令
python train_stage2_msf.py

# 训练配置:
# - 300 epochs
# - batch_size = 16  
# - workers = 8
# - 余弦学习率调度
# - 每20轮保存检查点
# - 最后30轮关闭mosaic增强
"""
    
    print(training_command)
    
    # 保存到文件
    with open("run_stage2_training.sh", "w") as f:
        f.write("#!/bin/bash\n")
        f.write("# 阶段2训练脚本\n")
        f.write("echo '🚀 开始阶段2训练...'\n")
        f.write("python train_stage2_msf.py\n")
        f.write("echo '🎉 阶段2训练完成!'\n")
    
    print("✅ 训练脚本已保存: run_stage2_training.sh")
    print()


def main():
    """主函数"""
    print("🚀 阶段2训练准备")
    print("="*60)
    print("配置: 300 epochs, 8 workers, batch_size=16")
    print("="*60)
    
    # 系统检查
    check_system_resources()
    check_dataset()
    check_stage1_weights()
    
    # 环境优化
    optimize_training_environment()
    
    # 时间估算
    estimate_training_time()
    
    # 生成训练脚本
    create_training_script()
    
    print("🎯 准备完成!")
    print("="*60)
    print("现在可以开始训练:")
    print("  方法1: python train_stage2_msf.py")
    print("  方法2: bash run_stage2_training.sh")
    print()
    print("💡 训练监控建议:")
    print("  - 使用 nvidia-smi 监控GPU使用率")
    print("  - 检查 runs/detect/stage2_msf/ 目录的训练日志")
    print("  - 每50轮检查一次验证性能")
    print("  - 如遇到内存不足，减小batch_size到12")
    
    # 询问是否立即开始训练
    response = input("\n❓ 是否立即开始阶段2训练? (y/N): ")
    if response.lower() == 'y':
        print("🎯 启动训练...")
        import subprocess
        subprocess.run([sys.executable, "train_stage2_msf.py"])
    else:
        print("✅ 准备完成，请手动启动训练")


if __name__ == '__main__':
    main()
