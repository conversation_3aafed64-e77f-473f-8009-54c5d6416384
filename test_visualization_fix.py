#!/usr/bin/env python3
"""
测试RGB+D可视化补丁修复
"""

import torch
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append('.')

# 导入并应用补丁
try:
    from ultralytics.data.rgbd_plotting_patch import apply_rgbd_visualization_patch
    apply_rgbd_visualization_patch()
    print("✅ RGB+D可视化补丁已应用")
except ImportError as e:
    print(f"❌ 无法导入RGB+D可视化补丁: {e}")
    sys.exit(1)

# 导入plotting函数
from ultralytics.utils.plotting import plot_images

def test_4channel_visualization():
    """测试4通道图像可视化"""
    print("\n🧪 测试4通道图像可视化...")
    
    # 创建模拟的4通道图像批次 [B, C, H, W]
    batch_size = 2
    height, width = 640, 640
    
    # 创建4通道图像 (RGB + Depth)
    images_4ch = torch.randn(batch_size, 4, height, width)
    images_4ch = torch.clamp(images_4ch, 0, 1)  # 归一化到[0,1]
    
    # 创建模拟的检测数据
    batch_idx = torch.tensor([0, 0, 1, 1])  # 每个图像2个检测
    cls = torch.tensor([0, 1, 2, 0])  # 类别
    bboxes = torch.tensor([
        [0.5, 0.5, 0.2, 0.3],  # [x_center, y_center, width, height]
        [0.3, 0.7, 0.15, 0.25],
        [0.6, 0.4, 0.18, 0.22],
        [0.8, 0.8, 0.12, 0.16]
    ])
    
    try:
        # 测试可视化
        result = plot_images(
            images=images_4ch,
            batch_idx=batch_idx,
            cls=cls,
            bboxes=bboxes,
            fname="test_4ch_visualization.jpg",
            names={0: 'Car', 1: 'Pedestrian', 2: 'Cyclist'},
            save=True
        )
        
        print("✅ 4通道图像可视化测试成功!")
        print(f"   输入形状: {images_4ch.shape}")
        print(f"   输出文件: test_4ch_visualization.jpg")
        
        # 检查文件是否生成
        if os.path.exists("test_4ch_visualization.jpg"):
            print("✅ 可视化文件已生成")
        else:
            print("⚠️ 可视化文件未生成")
            
    except Exception as e:
        print(f"❌ 4通道图像可视化测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_3channel_visualization():
    """测试3通道图像可视化（确保不破坏原有功能）"""
    print("\n🧪 测试3通道图像可视化...")
    
    # 创建模拟的3通道图像批次 [B, C, H, W]
    batch_size = 2
    height, width = 640, 640
    
    # 创建3通道图像 (RGB)
    images_3ch = torch.randn(batch_size, 3, height, width)
    images_3ch = torch.clamp(images_3ch, 0, 1)  # 归一化到[0,1]
    
    # 创建模拟的检测数据
    batch_idx = torch.tensor([0, 1])
    cls = torch.tensor([0, 1])
    bboxes = torch.tensor([
        [0.5, 0.5, 0.2, 0.3],
        [0.3, 0.7, 0.15, 0.25]
    ])
    
    try:
        # 测试可视化
        result = plot_images(
            images=images_3ch,
            batch_idx=batch_idx,
            cls=cls,
            bboxes=bboxes,
            fname="test_3ch_visualization.jpg",
            names={0: 'Car', 1: 'Pedestrian'},
            save=True
        )
        
        print("✅ 3通道图像可视化测试成功!")
        print(f"   输入形状: {images_3ch.shape}")
        print(f"   输出文件: test_3ch_visualization.jpg")
        
        # 检查文件是否生成
        if os.path.exists("test_3ch_visualization.jpg"):
            print("✅ 可视化文件已生成")
        else:
            print("⚠️ 可视化文件未生成")
            
    except Exception as e:
        print(f"❌ 3通道图像可视化测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("🎯 RGB+D可视化补丁测试")
    print("=" * 50)
    
    # 测试4通道图像
    test_4channel_visualization()
    
    # 测试3通道图像（确保向后兼容）
    test_3channel_visualization()
    
    print("\n🎉 测试完成!")

if __name__ == '__main__':
    main()
