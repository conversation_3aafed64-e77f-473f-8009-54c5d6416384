# YOLOv12m-RGB Baseline 🚀, AGPL-3.0 license
# YOLOv12m object detection model with RGB inputs only (baseline for comparison)
# 纯RGB基线模型，用于与RGB+Depth模型对比

# Parameters
nc: 3  # number of classes (KITTI: Car, Pede<PERSON>rian, Cyclist)
ch: 3  # input channels (RGB only)
scale: 'm'  # 明确指定使用YOLOv12m规模

# Model scaling - 使用标准YOLOv12格式
scales: # model compound scaling constants
  # [depth, width, max_channels]  
  n: [0.50, 0.25, 1024]
  s: [0.50, 0.50, 1024] 
  m: [0.50, 1.00, 512]  # Medium规模：depth=0.5, width=1.0, max_ch=512
  l: [1.00, 1.00, 512]
  x: [1.00, 1.50, 512]

# RGB-only Backbone (标准YOLOv12m架构)
backbone:
  # [from, repeats, module, args]
  # RGB处理 
  - [-1, 1, Conv,  [64, 3, 2]]   # 0-RGB P1/2 (处理3通道RGB输入)
  - [-1, 1, Conv,  [128, 3, 2, 1, 2]]  # 1-RGB P2/4  
  - [-1, 2, C3k2,  [256, False, 0.25]] # 2-R<PERSON> 
  - [-1, 1, Conv,  [256, 3, 2, 1, 4]]  # 3-RGB P3/8
  - [-1, 2, C3k2,  [512, False, 0.25]] # 4-RGB 
  - [-1, 1, Conv,  [512, 3, 2]]  # 5-RGB P4/16
  - [-1, 4, A2C2f, [512, True, 4]] # 6-RGB 
  - [-1, 1, Conv,  [1024, 3, 2]] # 7-RGB P5/32
  - [-1, 4, A2C2f, [1024, True, 1]] # 8-RGB 

# RGB-only Head (标准YOLOv12m头部)
head:
  # FPN上采样路径
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]] # 9-上采样
  - [[-1, 6], 1, Concat, [1]] # 10-连接P4特征
  - [-1, 2, A2C2f, [512, False, -1]] # 11-P4处理

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]] # 12-上采样  
  - [[-1, 4], 1, Concat, [1]] # 13-连接P3特征
  - [-1, 2, A2C2f, [256, False, -1]] # 14-P3处理 (最终P3)

  # PAN下采样路径
  - [-1, 1, Conv, [256, 3, 2]]    # 15-下采样
  - [[-1, 11], 1, Concat, [1]]    # 16-连接P4特征
  - [-1, 2, A2C2f, [512, False, -1]] # 17-P4最终特征

  - [-1, 1, Conv, [512, 3, 2]]    # 18-下采样
  - [[-1, 8], 1, Concat, [1]]     # 19-连接P5特征
  - [-1, 2, C3k2, [1024, True]]   # 20-P5最终特征

  # 检测头
  - [[14, 17, 20], 1, Detect, [nc]] # 21-Detect(P3, P4, P5)
