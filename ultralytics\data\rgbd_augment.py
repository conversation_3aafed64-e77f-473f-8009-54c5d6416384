# RGB+Depth 自定义数据增强管道
# 专门处理4通道RGB+D输入的数据增强

import cv2
import numpy as np
import random
from copy import deepcopy
import torch
from torchvision import transforms
from ultralytics.data.augment import LetterBox, Format, Instances
from ultralytics.utils.ops import xyxy2xywhn, xywhn2xyxy


class RGBDFormat(Format):
    """
    扩展Format类以正确处理4通道RGB+D图像
    避免通道翻转破坏RGB和Depth的顺序
    """

    def _format_img(self, img):
        """
        格式化4通道RGB+D图像，避免破坏通道顺序

        Args:
            img (np.ndarray): 4通道输入图像 [H, W, 4]

        Returns:
            torch.Tensor: 格式化后的图像 [4, H, W]
        """
        if len(img.shape) < 3:
            img = np.expand_dims(img, -1)

        # 转置为CHW格式
        img = img.transpose(2, 0, 1)  # [H, W, C] -> [C, H, W]

        # 对于4通道图像，只对RGB通道应用BGR翻转，保持Depth通道不变
        if img.shape[0] == 4:
            rgb_channels = img[:3]  # 前3个通道是RGB
            depth_channel = img[3:4]  # 第4个通道是Depth

            # 只对RGB通道应用BGR翻转
            if random.uniform(0, 1) > self.bgr:
                rgb_channels = rgb_channels[::-1]  # BGR翻转

            # 重新组合
            img = np.concatenate([rgb_channels, depth_channel], axis=0)
        else:
            # 对于3通道或其他，使用原始逻辑
            img = img[::-1] if random.uniform(0, 1) > self.bgr else img

        img = np.ascontiguousarray(img)
        img = torch.from_numpy(img)
        return img


class RGBDTransforms:
    """
    RGB+Depth专用数据增强管道
    分别处理RGB和Depth通道，避免不兼容的颜色变换
    """
    
    def __init__(self, imgsz=640, augment=True, hyp=None):
        self.imgsz = imgsz
        self.augment = augment
        self.hyp = hyp or {}

        # 基础变换（总是应用）
        self.letterbox = LetterBox(new_shape=(imgsz, imgsz), scaleup=False)
        self.format = RGBDFormat(  # 使用自定义的RGBDFormat
            bbox_format="xywh",
            normalize=True,
            return_mask=False,
            return_keypoint=False,
            return_obb=False,
            batch_idx=True,
            mask_ratio=self.hyp.get('mask_ratio', 4),
            mask_overlap=self.hyp.get('overlap_mask', True),
            bgr=0.0  # 不应用BGR转换，因为我们有4通道
        )
    
    def __call__(self, labels):
        """
        应用RGB+D数据增强
        
        Args:
            labels (dict): 包含4通道图像和标签的字典
                          labels['img']: [H, W, 4] numpy array (RGB+D)
                          
        Returns:
            dict: 增强后的标签字典
        """
        img = labels['img']  # [H, W, 4]
        
        # 检查输入格式
        if img.shape[-1] != 4:
            raise ValueError(f"RGBDTransforms期望4通道输入，得到{img.shape[-1]}通道")
        
        h, w = img.shape[:2]
        
        # 分离RGB和Depth
        rgb_img = img[:, :, :3].copy()  # [H, W, 3]
        depth_img = img[:, :, 3:4].copy()  # [H, W, 1]
        
        if self.augment:
            # 对RGB和Depth应用几何增强（翻转、缩放等）
            rgb_img, depth_img, labels = self._apply_geometric_augmentations(
                rgb_img, depth_img, labels
            )
            
            # 只对RGB应用颜色增强
            rgb_img = self._apply_color_augmentations(rgb_img)
            
            # 对Depth应用适当的增强（避免颜色变换）
            depth_img = self._apply_depth_augmentations(depth_img)
        
        # 重新组合为4通道
        img_combined = np.concatenate([rgb_img, depth_img], axis=2)
        labels['img'] = img_combined
        
        # 应用LetterBox（保持宽高比的resize）
        labels = self.letterbox(labels)
        
        # 最终格式化
        labels = self.format(labels)
        
        return labels
    
    def _apply_geometric_augmentations(self, rgb_img, depth_img, labels):
        """
        应用几何增强（翻转、旋转等），同时处理RGB和Depth
        """
        h, w = rgb_img.shape[:2]
        
        # 确保depth_img保持3维 [H, W, 1]
        if depth_img.ndim == 2:
            depth_img = np.expand_dims(depth_img, axis=2)
        
        # 水平翻转
        if self.augment and random.random() < self.hyp.get('fliplr', 0.5):
            rgb_img = np.fliplr(rgb_img)
            depth_img = np.fliplr(depth_img)
            
            # 翻转边界框（如果存在）
            if 'bboxes' in labels and len(labels['bboxes']) > 0:
                bboxes = labels['bboxes']
                if hasattr(bboxes, 'shape') and len(bboxes.shape) > 1:
                    # 假设边界框格式为 [x_center, y_center, width, height] (normalized)
                    bboxes[:, 0] = 1 - bboxes[:, 0]  # x坐标翻转
                    labels['bboxes'] = bboxes
        
        # 垂直翻转（较少使用）
        if self.augment and random.random() < self.hyp.get('flipud', 0.0):
            rgb_img = np.flipud(rgb_img)
            depth_img = np.flipud(depth_img)
            
            # 翻转边界框（如果存在）
            if 'bboxes' in labels and len(labels['bboxes']) > 0:
                bboxes = labels['bboxes']
                if hasattr(bboxes, 'shape') and len(bboxes.shape) > 1:
                    bboxes[:, 1] = 1 - bboxes[:, 1]  # y坐标翻转
                    labels['bboxes'] = bboxes
        
        # 确保返回的depth_img保持 [H, W, 1] 格式
        if depth_img.ndim == 2:
            depth_img = np.expand_dims(depth_img, axis=2)
        
        return rgb_img, depth_img, labels
    
    def _apply_color_augmentations(self, rgb_img):
        """
        只对RGB通道应用颜色增强
        """
        if not self.augment:
            return rgb_img
            
        # HSV增强
        hsv_h = self.hyp.get('hsv_h', 0.015)
        hsv_s = self.hyp.get('hsv_s', 0.7) 
        hsv_v = self.hyp.get('hsv_v', 0.4)
        
        if hsv_h or hsv_s or hsv_v:
            hsv = cv2.cvtColor(rgb_img, cv2.COLOR_RGB2HSV)
            
            # 随机调整HSV
            if hsv_h > 0:
                h_gain = np.random.uniform(-hsv_h, hsv_h)
                hsv[:, :, 0] = (hsv[:, :, 0] + h_gain * 179) % 180
            
            if hsv_s > 0:
                s_gain = np.random.uniform(1 - hsv_s, 1 + hsv_s)
                hsv[:, :, 1] = np.clip(hsv[:, :, 1] * s_gain, 0, 255)
            
            if hsv_v > 0:
                v_gain = np.random.uniform(1 - hsv_v, 1 + hsv_v)
                hsv[:, :, 2] = np.clip(hsv[:, :, 2] * v_gain, 0, 255)
            
            rgb_img = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)
        
        return rgb_img
    
    def _apply_depth_augmentations(self, depth_img):
        """
        对Depth通道应用适当的增强（避免颜色变换）
        """
        if not self.augment:
            return depth_img
        
        # 确保depth_img保持3维 [H, W, 1]
        if depth_img.ndim == 2:
            depth_img = np.expand_dims(depth_img, axis=2)
            
        # 深度图噪声（轻微）
        if random.random() < 0.1:
            noise = np.random.normal(0, 2, depth_img.shape).astype(np.uint8)
            depth_img = np.clip(depth_img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
        
        # 深度图轻微模糊（模拟传感器噪声）
        if random.random() < 0.1:
            # 对于3维图像，只模糊前两个维度
            if depth_img.ndim == 3:
                depth_blurred = cv2.GaussianBlur(depth_img[:,:,0], (3, 3), 0.5)
                depth_img[:,:,0] = depth_blurred
            else:
                depth_img = cv2.GaussianBlur(depth_img, (3, 3), 0.5)
                depth_img = np.expand_dims(depth_img, axis=2)
        
        # 确保返回的depth_img保持 [H, W, 1] 格式
        if depth_img.ndim == 2:
            depth_img = np.expand_dims(depth_img, axis=2)
        
        return depth_img


class RGBDVisualization:
    """
    RGB+D数据可视化工具
    只显示RGB部分，避免4通道显示问题
    """
    
    @staticmethod
    def extract_rgb_for_visualization(img_4ch):
        """
        从4通道图像中提取RGB用于可视化
        
        Args:
            img_4ch: [C, H, W] tensor 或 [H, W, C] numpy array
            
        Returns:
            rgb_img: 3通道RGB图像用于可视化
        """
        if isinstance(img_4ch, torch.Tensor):
            if img_4ch.shape[0] == 4:  # [C, H, W]
                return img_4ch[:3]  # 取前3个通道
            elif img_4ch.shape[-1] == 4:  # [H, W, C] 
                return img_4ch[:, :, :3]
        elif isinstance(img_4ch, np.ndarray):
            if img_4ch.shape[-1] == 4:  # [H, W, C]
                return img_4ch[:, :, :3]
            elif img_4ch.shape[0] == 4:  # [C, H, W]
                return img_4ch[:3]
        
        return img_4ch  # 如果不是4通道，直接返回
    
    @staticmethod 
    def visualize_rgbd_sample(rgb_img, depth_img, bboxes=None, save_path=None):
        """
        可视化RGB+D样本（并排显示RGB和Depth）
        """
        import matplotlib.pyplot as plt
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
        
        # RGB图像
        ax1.imshow(rgb_img)
        ax1.set_title('RGB Image')
        ax1.axis('off')
        
        # 深度图像
        ax2.imshow(depth_img.squeeze() if depth_img.ndim == 3 else depth_img, cmap='plasma')
        ax2.set_title('Depth Image')
        ax2.axis('off')
        
        # 添加边界框（如果有）
        if bboxes is not None:
            for bbox in bboxes:
                # bbox格式: [x_center, y_center, width, height] (normalized)
                x, y, w, h = bbox
                x1 = (x - w/2) * rgb_img.shape[1]
                y1 = (y - h/2) * rgb_img.shape[0]
                w_abs = w * rgb_img.shape[1]
                h_abs = h * rgb_img.shape[0]
                
                rect1 = plt.Rectangle((x1, y1), w_abs, h_abs, 
                                    linewidth=2, edgecolor='red', facecolor='none')
                rect2 = plt.Rectangle((x1, y1), w_abs, h_abs,
                                    linewidth=2, edgecolor='red', facecolor='none')
                ax1.add_patch(rect1)
                ax2.add_patch(rect2)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            plt.close()
        else:
            plt.show()