# KITTI RGB-only Dataset Configuration for YOLOv12 Baseline
# 纯RGB基线模型配置，用于与RGB+Depth模型对比

path: /home/<USER>/kitti  # 服务器数据集根目录
train: images/train_set  # 训练集图像目录
val: images/val_set      # 验证集图像目录

# 注意：不包含深度图配置，纯RGB输入

# 数据增强配置（标准RGB增强）
hsv_h: 0.015            # 色调增强范围
hsv_s: 0.7              # 饱和度增强范围  
hsv_v: 0.4              # 明度增强范围
degrees: 0.0            # 旋转角度
translate: 0.1          # 平移范围
scale: 0.5              # 缩放范围
shear: 0.0              # 剪切
perspective: 0.0        # 透视变换
flipud: 0.0             # 垂直翻转概率
fliplr: 0.5             # 水平翻转概率
mosaic: 1.0             # Mosaic增强概率
mixup: 0.0              # Mixup增强概率
copy_paste: 0.0         # Copy-paste增强概率

# KITTI数据集类别
nc: 3  # number of classes
names: ['Car', 'Pedestrian', 'Cyclist'] # class names

# 类别权重（可选，用于处理类别不平衡）
class_weights: [1.0, 2.0, 2.0]  # 对应 Car, Pedestrian, Cyclist
